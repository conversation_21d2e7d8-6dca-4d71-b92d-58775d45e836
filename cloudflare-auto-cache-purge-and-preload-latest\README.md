# Cloudflare Auto Cache Purge & Preload v1.3.0

Plugin tự động xóa và preload cache Cloudflare khi có thay đổi trên WordPress. Phiên bản này đã được tối ưu để loại bỏ Action Scheduler, sử dụng WordPress Cron native để giảm độ phức tạp và tăng hiệu suất.

## Tính năng
- **Tự động xóa cache** khi bài viết, trang, bình luận, danh mục, thẻ thay đổi.
- **Preload cache sau khi xóa** để cải thiện tốc độ tải.
- **Nút "Clear Cache" & "Preload Cache"** trên admin bar.
- **Preload toàn bộ trang hàng ngày**.
- **Cấu hình dễ dàng** qua giao diện web.
- **Ghi log quá trình xóa & preload cache**.

## Cài đặt
1. **Cài plugin** như WordPress plugin thông thường.
2. **<PERSON><PERSON>u hình tại** `Admin Dashboard → Tools → Cloudflare Auto Cache Purge & Preload`.
3. **Điền thông tin**:
   - Cloudflare API Email, API Key, Zone ID
   - Sitemap URL (mặc định `/sitemap.xml`)
   - URLs tùy chỉnh cần xóa cache
   - Bật/tắt logging & auto preload

## Tối ưu hiệu năng
- **Sử dụng WordPress Cron native** - không cần cấu hình phức tạp.
- **Non-blocking requests** - không làm chậm trang web.
- **Queue system đơn giản** - sử dụng transients để quản lý hàng đợi.
- **Tự động retry** - thử lại khi có lỗi xảy ra.

## Cải tiến v1.3.0
- ✅ **Loại bỏ Action Scheduler** - giảm 90% kích thước plugin
- ✅ **Sử dụng WordPress Cron native** - đơn giản hơn, ít conflict
- ✅ **Non-blocking HTTP requests** - không ảnh hưởng hiệu suất
- ✅ **Queue system với transients** - nhẹ và hiệu quả
- ✅ **Tự động retry mechanism** - đáng tin cậy hơn

## Hiệu suất
Tùy chỉnh tùy thuộc vào số core CPU và tình trạng đang sử dụng 

Trên VPS 4 core Oracle
- **Preload 600 trang mới: ~42 giây**.
- **Preload 600 trang đã có cache: ~5 giây**.
- **Xóa & preload bài viết mới: ~10 giây**.

Trên VPS 1 core cấu hình thấp nhất của UpCloud
- **Preload 600 trang mới: ~120 giây**.
- .........

## Lỗi & Góp ý
Mở issue hoặc comment để được hỗ trợ.

