# Cloudflare Auto Cache Purge & Preload v1.3.0

Plugin tự động xóa và preload cache Cloudflare khi có thay đổi trên WordPress. Phiên bản này đã được tối ưu để loại bỏ Action Scheduler, sử dụng WordPress Cron native để giảm độ phức tạp và tăng hiệu suất.

## Tính năng
- **Tự động xóa cache** khi bài viết, trang, bình luận, danh mục, thẻ thay đổi.
- **Preload cache sau khi xóa** để cải thiện tốc độ tải.
- **Nút "Clear Cache" & "Preload Cache"** trên admin bar.
- **🆕 Tạo Cache Rules tự động** - một click setup cache rules cho WordPress.
- **Preload toàn bộ trang hàng ngày**.
- **<PERSON><PERSON><PERSON> hì<PERSON> dễ dàng** qua giao diện web.
- **Ghi log quá trình xóa & preload cache**.

## Cài đặt
1. **Cài plugin** như WordPress plugin thông thường.
2. **C<PERSON>u hình tại** `Admin Dashboard → Tools → Cloudflare Auto Cache Purge & Preload`.
3. **Điền thông tin**:
   - Cloudflare API Email, API Key, Zone ID
   - Sitemap URL (mặc định `/sitemap.xml`)
   - URLs tùy chỉnh cần xóa cache
   - Bật/tắt logging & auto preload
4. **🆕 Tạo Cache Rules** (tùy chọn):
   - Nhấn nút "⚙️ Cache Rules" → "➕ Tạo Cache Rule" trên admin bar
   - Hoặc sử dụng giao diện trong trang settings

## Tối ưu hiệu năng
- **Sử dụng WordPress Cron native** - không cần cấu hình phức tạp.
- **Non-blocking requests** - không làm chậm trang web.
- **Queue system đơn giản** - sử dụng transients để quản lý hàng đợi.
- **Tự động retry** - thử lại khi có lỗi xảy ra.

## Cải tiến v1.3.0
- ✅ **Loại bỏ Action Scheduler** - giảm 90% kích thước plugin
- ✅ **Sử dụng WordPress Cron native** - đơn giản hơn, ít conflict
- ✅ **Non-blocking HTTP requests** - không ảnh hưởng hiệu suất
- ✅ **Queue system với transients** - nhẹ và hiệu quả
- ✅ **Tự động retry mechanism** - đáng tin cậy hơn
- 🆕 **Cache Rules tự động** - tạo cache rules tối ưu cho WordPress
- 🆕 **Tự động tắt Page Rules conflict** - tránh xung đột với Page Rules cũ

## Hiệu suất
Tùy chỉnh tùy thuộc vào số core CPU và tình trạng đang sử dụng 

Trên VPS 4 core Oracle
- **Preload 600 trang mới: ~42 giây**.
- **Preload 600 trang đã có cache: ~5 giây**.
- **Xóa & preload bài viết mới: ~10 giây**.

Trên VPS 1 core cấu hình thấp nhất của UpCloud
- **Preload 600 trang mới: ~120 giây**.
- .........

## 🆕 Cache Rules Tự Động

### Tính năng mới v1.3.0
Plugin giờ đây có thể **tự động tạo Cache Rules** tối ưu cho WordPress với một click!

### Cách sử dụng:
1. **Từ Admin Bar**: Nhấn "⚙️ Cache Rules" → "➕ Tạo Cache Rule"
2. **Từ Settings**: Vào trang cài đặt plugin, xem phần "Quản lý Cache Rules"

### Cache Rules sẽ:
#### ✅ **Cache các trang:**
- Trang chủ và trang nội dung công khai
- Posts, pages, archives, categories, tags
- CSS, JS, images và static files
- TTL: 1 năm (******** giây)

#### 🚫 **Loại trừ các trang:**
- Admin pages (`/wp-admin`, `/wp-login`)
- API endpoints (`/wp-json/`, `/wc-api/`, `/edd-api/`)
- E-commerce pages (`/checkout/`, `/cart/`, `/account/`)
- Member areas (`/dashboard/`, `/members-area/`)
- Users đã đăng nhập (có cookie `wordpress_logged_in_`)
- Trang có query parameters đặc biệt (`s=`, `p=`, `nocache`)

### Tự động xử lý conflicts:
- **Tắt Page Rules cũ** có "Cache Everything" để tránh xung đột
- **Kiểm tra rule tồn tại** - không tạo duplicate
- **Lưu URL dashboard** để dễ quản lý sau này

### Xóa Cache Rules:
- Nhấn "⚙️ Cache Rules" → "🗑️ Xóa Cache Rule" từ admin bar
- Hoặc xóa thủ công từ Cloudflare Dashboard

## Lỗi & Góp ý
Mở issue hoặc comment để được hỗ trợ.

