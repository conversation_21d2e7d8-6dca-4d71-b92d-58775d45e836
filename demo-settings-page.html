<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo - Cloudflare Auto Cache Purge & Preload Settings</title>
    <style>
        body { font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif; margin: 20px; background: #f1f1f1; }
        .wrap { background: white; padding: 20px; border-radius: 5px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); max-width: 1000px; }
        h1 { color: #23282d; border-bottom: 1px solid #ddd; padding-bottom: 10px; }
        h2 { color: #23282d; margin-top: 30px; }
        .form-table { width: 100%; }
        .form-table th { text-align: left; padding: 20px 10px 20px 0; width: 200px; vertical-align: top; }
        .form-table td { padding: 15px 10px; }
        .regular-text { width: 25em; padding: 3px 5px; }
        .button { background: #0073aa; color: white; border: none; padding: 8px 12px; border-radius: 3px; cursor: pointer; text-decoration: none; display: inline-block; }
        .button-primary { background: #0073aa; }
        .button-secondary { background: #f7f7f7; color: #555; border: 1px solid #ccc; }
        .button:hover { opacity: 0.9; }
        .notice { padding: 12px; margin: 5px 0 15px; border-left: 4px solid #0073aa; background: #fff; }
        .notice-success { border-left-color: #46b450; }
        .notice-warning { border-left-color: #ffb900; }
        .notice-error { border-left-color: #dc3232; }
    </style>
</head>
<body>
    <div class="wrap">
        <h1>Cài đặt Cloudflare Auto Cache Purge And Preload</h1>

        <form action="options.php" method="post">
            
            <h2>Cài đặt Cloudflare API</h2>
            <p>Nhập thông tin xác thực Cloudflare API của bạn:</p>
            <table class="form-table">
                <tr>
                    <th><label for="cloudflare_email">Cloudflare API Email</label></th>
                    <td><input type="email" id="cloudflare_email" name="cloudflare_cache_options[email]" value="<EMAIL>" class="regular-text" required></td>
                </tr>
                <tr>
                    <th><label for="cloudflare_api_key">Cloudflare API Key</label></th>
                    <td><input type="password" id="cloudflare_api_key" name="cloudflare_cache_options[api_key]" value="••••••••••••••••" class="regular-text" required></td>
                </tr>
                <tr>
                    <th><label for="cloudflare_zone_id">Cloudflare Zone ID</label></th>
                    <td><input type="text" id="cloudflare_zone_id" name="cloudflare_cache_options[zone_id]" value="abc123def456ghi789" class="regular-text" required></td>
                </tr>
            </table>

            <h2>Cài đặt URL</h2>
            <p>Cấu hình các URL cần xử lý:</p>
            <table class="form-table">
                <tr>
                    <th><label for="sitemap_url">Sitemap URL</label></th>
                    <td><input type="url" id="sitemap_url" name="cloudflare_cache_options[sitemap_url]" value="https://example.com/sitemap.xml" class="regular-text"></td>
                </tr>
                <tr>
                    <th><label for="custom_urls">Custom URLs để Purge</label></th>
                    <td>
                        <textarea id="custom_urls" name="cloudflare_cache_options[custom_urls]" rows="5" cols="50" style="width: 25em;">/special-page/
/api/custom/
https://example.com/external-page/</textarea>
                        <p style="font-style: italic; color: #666;">Nhập mỗi URL trên một dòng, sử dụng đường dẫn tương đối (ví dụ: /archives/) hoặc URL tuyệt đối</p>
                    </td>
                </tr>
            </table>

            <h2>Cài đặt khác</h2>
            <p>Các cài đặt khác:</p>
            <table class="form-table">
                <tr>
                    <th><label for="enable_logging">Logging</label></th>
                    <td><input type="checkbox" id="enable_logging" name="cloudflare_cache_options[enable_logging]" value="1" checked></td>
                </tr>
                <tr>
                    <th><label for="auto_preload">Auto Preload</label></th>
                    <td><input type="checkbox" id="auto_preload" name="cloudflare_cache_options[auto_preload]" value="1" checked></td>
                </tr>
                <tr>
                    <th><label for="auto_preload_time">Thời gian chạy Auto Preload</label></th>
                    <td>
                        <select id="auto_preload_time" name="cloudflare_cache_options[auto_preload_time]">
                            <option value="02:00" selected>02:00</option>
                            <option value="03:00">03:00</option>
                            <option value="04:00">04:00</option>
                        </select>
                        <p style="font-style: italic; color: #666;">Chọn giờ để chạy Auto Preload (theo múi giờ WordPress).</p>
                    </td>
                </tr>
            </table>

            <h2>🆕 Quản lý Cache Rules</h2>
            <p>Tạo và quản lý Cache Rules tự động cho WordPress:</p>
            
            <div style="background: #f9f9f9; padding: 15px; border-left: 4px solid #0073aa; margin: 10px 0;">
                <h4 style="margin-top: 0;">🚀 Cache Rules cho WordPress</h4>
                <p>Cache Rules giúp tối ưu hiệu suất website bằng cách tự động cache các trang phù hợp và loại trừ các trang admin, login, checkout...</p>
                
                <div style="background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; border-radius: 4px; margin: 10px 0;">
                    <strong>⚠️ Chưa có Cache Rule</strong><br>
                    Nhấn nút "Tạo Cache Rule" bên dưới để tạo rule tự động.
                </div>
                
                <div style="margin-top: 15px;">
                    <h5>📋 Rule sẽ cache:</h5>
                    <ul style="margin-left: 20px;">
                        <li>✅ Tất cả trang công khai (posts, pages, archives)</li>
                        <li>✅ Trang chủ và các trang danh mục</li>
                        <li>✅ CSS, JS, images và static files</li>
                    </ul>
                    
                    <h5>🚫 Rule sẽ loại trừ:</h5>
                    <ul style="margin-left: 20px;">
                        <li>❌ Trang admin (/wp-admin, /wp-login)</li>
                        <li>❌ API endpoints (/wp-json, /wc-api)</li>
                        <li>❌ Trang checkout, cart, account</li>
                        <li>❌ Users đã đăng nhập</li>
                        <li>❌ Trang có query parameters đặc biệt</li>
                    </ul>
                </div>
                
                <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #ddd;">
                    <strong>💡 Lưu ý:</strong> Cache Rule sẽ tự động tắt các Page Rules có "Cache Everything" để tránh conflict.
                </div>
            </div>

            <h2>🚀 Thao tác nhanh</h2>
            <p>Các thao tác nhanh để quản lý cache và rules:</p>
            
            <div style="background: #f9f9f9; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
                <h4 style="margin-top: 0; color: #0073aa;">🚀 Thao tác nhanh</h4>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                    <!-- Cache Rules Actions -->
                    <div style="background: white; padding: 15px; border-radius: 5px; border: 1px solid #ddd;">
                        <h5 style="margin-top: 0; color: #d63384;">⚙️ Cache Rules</h5>
                        <p style="color: #856404; background: #fff3cd; padding: 8px; border-radius: 3px; font-size: 13px;">
                            ⚠️ Chưa có Cache Rule
                        </p>
                        <a href="#" class="button button-primary" style="width: 100%;">
                            ➕ Tạo Cache Rule
                        </a>
                    </div>
                    
                    <!-- Cache Management Actions -->
                    <div style="background: white; padding: 15px; border-radius: 5px; border: 1px solid #ddd;">
                        <h5 style="margin-top: 0; color: #0d6efd;">🗂️ Quản lý Cache</h5>
                        <a href="#" class="button button-secondary" style="width: 100%; margin-bottom: 5px;">
                            🗑️ Clear All Cache
                        </a>
                        <a href="#" class="button button-secondary" style="width: 100%;">
                            ♻️ Preload Cache
                        </a>
                    </div>
                </div>
                
                <div style="background: #e7f3ff; padding: 10px; border-radius: 3px; font-size: 13px;">
                    <strong>💡 Mẹo:</strong> Các nút này cũng có sẵn trên Admin Bar (thanh công cụ phía trên) khi bạn đăng nhập với quyền Administrator.
                </div>
            </div>

            <p style="margin-top: 30px;">
                <input type="submit" name="submit" id="submit" class="button button-primary" value="Lưu cài đặt">
            </p>
        </form>

        <hr>
        <h2>Reset về mặc định</h2>
        <p>Nhấn nút dưới đây để khôi phục về cài đặt mặc định.</p>
        <form action="" method="post">
            <input type="hidden" name="action" value="reset_cloudflare_settings">
            <input type="hidden" name="_wpnonce" value="abc123">
            <button type="submit" class="button button-secondary">
                Reset về mặc định
            </button>
        </form>
    </div>

    <script>
        // Toggle auto preload time field
        document.getElementById('auto_preload').addEventListener('change', function() {
            const timeField = document.getElementById('auto_preload_time').closest('tr');
            if (this.checked) {
                timeField.style.display = '';
            } else {
                timeField.style.display = 'none';
            }
        });
    </script>
</body>
</html>
