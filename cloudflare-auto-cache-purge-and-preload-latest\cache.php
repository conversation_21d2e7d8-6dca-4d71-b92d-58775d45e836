<?php
/**
 * Plugin Name: Cloudflare Auto Cache Purge And Preload
 * Description: Tự động xóa và preload cache cho bài viết, trang, danh mục và thẻ sử dụng Cloudflare API và WordPress Cron.
 * Version: 1.3.1
 * Author: bibica
 * Author URI: https://bibica.net
 * Plugin URI: https://bibica.net/cloudflare-auto-cache-purge-and-preload
 * Text Domain: cloudflare-auto-cache-purge-and-preload
 * License: GPL-3.0
 * License URI: http://www.gnu.org/licenses/gpl-3.0.txt
 */

if (!defined('ABSPATH')) {
    exit;
}

define('CF_PURGE_LOG', WP_CONTENT_DIR . '/cloudflare_purge_preload.log');

class Cloudflare_Auto_Cache_Purge_And_Preload {
    private $supported_post_types = ['post', 'page'];
    const ACTION_PURGE_URLS = 'cf_purge_urls_action';
    const ACTION_PRELOAD_URLS = 'cf_preload_urls_action';
    const MAX_URLS_PER_BATCH = 30;
    
    public function __construct() {
        // Khởi tạo settings
        add_action('admin_menu', [$this, 'add_admin_menu']);
        add_action('admin_init', [$this, 'register_settings']);
		add_action('admin_init', [$this, 'handle_reset_settings']);
		add_filter('plugin_action_links_' . plugin_basename(__FILE__), [$this, 'add_settings_link']);

		add_action('wp_after_insert_post', [$this, 'handle_save_post'], 10, 4);
        add_action('before_delete_post', [$this, 'handle_before_delete_post']);
        add_action('edited_term', [$this, 'handle_edit_term'], 10, 3);
        add_action('delete_term', [$this, 'handle_delete_term'], 10, 4);
        add_action('wp_trash_post', [$this, 'handle_trash_post']);
        add_action('transition_post_status', [$this, 'handle_status_change'], 10, 3);

		// Thêm hook xử lý comment
		add_action('transition_comment_status', [$this, 'handle_comment_status_change'], 10, 3);
		add_action('comment_post', [$this, 'handle_new_comment'], 10, 3);
		add_action('edit_comment', [$this, 'handle_comment_edit'], 10, 2);
		add_action('delete_comment', [$this, 'handle_comment_deletion'], 10, 2);

        // Đăng ký các action hooks cho cache processing
        add_action(self::ACTION_PURGE_URLS, [$this, 'process_purge_urls_batch']);
        add_action(self::ACTION_PRELOAD_URLS, [$this, 'process_preload_urls_batch']);
        add_action('cf_process_cache_queue', [$this, 'process_cache_queue']);

		// Add new hooks for admin bar and notices
		add_action('admin_bar_menu', [$this, 'add_admin_bar_cache_buttons'], 90);
        add_action('admin_notices', [$this, 'display_admin_notices']);

        // Đăng ký xử lý admin post
        add_action('admin_post_cloudflare_clear_cache', [$this, 'handle_cloudflare_clear_cache']);
        add_action('admin_post_cloudflare_preload_cache', [$this, 'handle_cloudflare_preload_cache']);
        add_action('admin_post_cloudflare_create_cache_rule', [$this, 'handle_create_cache_rule']);
        add_action('admin_post_cloudflare_delete_cache_rule', [$this, 'handle_delete_cache_rule']);

        // Đăng ký action cho preload sitemap
        add_action('cloudflare_preload_sitemap_action', [$this, 'preload_sitemap_urls']);

		// Đăng ký auto preload sitemap
		add_action('cloudflare_daily_preload_cache', [$this, 'handle_cloudflare_daily_preload_cache']);
		add_action('update_option_cloudflare_cache_options', [$this, 'update_cloudflare_preload_cron'], 10, 2);
		register_deactivation_hook(__FILE__, [$this, 'deactivate_cloudflare_preload_cache']);
		register_activation_hook(__FILE__, [$this, 'schedule_cloudflare_preload_cache']);

        if (defined('WP_CLI') && WP_CLI) {
            $this->register_cli_commands();
        }
    }

    private function register_cli_commands() {
        WP_CLI::add_command('cloudflare purge', function($args, $assoc_args) {
            $queue = get_transient('cf_cache_queue') ?: ['purge' => [], 'preload' => []];
            if (empty($queue['purge'])) {
                WP_CLI::success('Không có URL nào cần xóa cache.');
                return;
            }

            $this->process_purge_urls_batch($queue['purge']);
            delete_transient('cf_cache_queue');
            WP_CLI::success('Đã xóa cache cho ' . count($queue['purge']) . ' URLs.');
        });

        WP_CLI::add_command('cloudflare preload', function($args, $assoc_args) {
            $queue = get_transient('cf_cache_queue') ?: ['purge' => [], 'preload' => []];
            if (empty($queue['preload'])) {
                WP_CLI::success('Không có URL nào cần preload.');
                return;
            }

            $this->process_preload_urls_batch($queue['preload']);
            delete_transient('cf_cache_queue');
            WP_CLI::success('Đã preload ' . count($queue['preload']) . ' URLs.');
        });
    }


   private function log_message($message) {
        try {
            $options = get_option('cloudflare_cache_options');
            if (isset($options['enable_logging']) && $options['enable_logging'] === '1') {
                $log_time = current_time('mysql');
                $log = "[" . $log_time . "] " . $message . PHP_EOL;
                file_put_contents(CF_PURGE_LOG, $log, FILE_APPEND);
            }
        } catch (Exception $e) {
            error_log("Lỗi ghi log Cloudflare Cache: " . $e->getMessage());
        }
    }

    public function handle_status_change($new_status, $old_status, $post) {
        try {
            if ($new_status === $old_status || !in_array($post->post_type, $this->supported_post_types)) {
                return;
            }

            if ($old_status === 'trash' && $new_status === 'publish') {
                $urls = $this->collect_urls_to_purge($post);
                $this->schedule_urls_processing($urls);
            }
        } catch (Exception $e) {
            $this->log_message("Lỗi xử lý thay đổi trạng thái: " . $e->getMessage());
        }
    }
	/**
	 * Xử lý khi trạng thái comment thay đổi
	 */
	public function handle_comment_status_change($new_status, $old_status, $comment) {
		try {
			// Clear cache khi comment được chuyển sang approved từ bất kỳ trạng thái nào khác
			if ($new_status === 'approved' && $old_status !== 'approved') {
				$this->purge_cache_for_comment($comment);
			}
			// Clear cache khi comment được chuyển từ approved sang trạng thái khác
			else if ($old_status === 'approved' && $new_status !== 'approved') {
				$this->purge_cache_for_comment($comment);
			}
		} catch (Exception $e) {
			$this->log_message("Lỗi xử lý thay đổi trạng thái comment: " . $e->getMessage());
		}
	}

	/**
	 * Xử lý khi có comment mới được đăng mà không cần phê duyệt
	 */
	public function handle_new_comment($comment_id, $comment_approved, $commentdata) {
		try {
			// Chỉ xử lý khi comment được tự động phê duyệt
			if ($comment_approved === 1) {
				$comment = get_comment($comment_id);
				$this->purge_cache_for_comment($comment);
			}
		} catch (Exception $e) {
			$this->log_message("Lỗi xử lý comment mới: " . $e->getMessage());
		}
	}
	/**
	 * Xử lý khi comment được sửa
	 */
	public function handle_comment_edit($comment_id, $commentdata) {
		try {
			// Lấy thông tin comment
			$comment = get_comment($comment_id);

			// Xóa cache liên quan đến comment
			$this->purge_cache_for_comment($comment);
		} catch (Exception $e) {
			$this->log_message("Lỗi xử lý sửa comment: " . $e->getMessage());
		}
	}
	/**
	 * Xử lý khi comment bị xóa hoàn toàn
	 */
	public function handle_comment_deletion($comment_id, $comment) {
		try {
			// Chỉ clear cache nếu comment đang ở trạng thái approved
			if ($comment->comment_approved === '1') {
				$this->purge_cache_for_comment($comment);
			}
		} catch (Exception $e) {
			$this->log_message("Lỗi xử lý xóa comment: " . $e->getMessage());
		}
	}

	/**
	 * Xóa cache liên quan đến comment
	 */
	private function purge_cache_for_comment($comment) {
		try {
			// Lấy post ID từ comment
			$post_id = $comment->comment_post_ID;

			// Lấy URL của bài viết
			$post_url = get_permalink($post_id);

			if ($post_url) {
				// Xóa cache của URL bài viết
				$this->schedule_urls_processing([$post_url]);

				// Nếu có trang phân trang comment, xóa cache của các trang phân trang đó
				$comment_pages = get_comment_pages_count($comment);
				if ($comment_pages > 1) {
					for ($i = 2; $i <= $comment_pages; $i++) {
						$this->schedule_urls_processing([$post_url . 'comment-page-' . $i . '/']);
					}
				}
			}
		} catch (Exception $e) {
			$this->log_message("Lỗi xóa cache cho comment: " . $e->getMessage());
		}
	}	

	public function handle_save_post($post_id, $post, $update, $post_before) {
	   try {
		   // Chặn nếu là bản revision, autosave hoặc đang trong quá trình autosave
		   if (wp_is_post_revision($post_id) || wp_is_post_autosave($post_id) || (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE)) {
			   return;
		   }
		   // Chặn nếu bài viết đang là draft, pending hoặc auto-draft (vì chưa public, không cần xóa cache)
		   if (in_array($post->post_status, ['draft', 'pending', 'auto-draft'])) {
			   return;
		   }
		   // Chỉ xử lý nếu post_type thuộc danh sách được hỗ trợ
		   if (in_array($post->post_type, $this->supported_post_types)) {
			   // Xử lý URL cache ngay lập tức
			   $urls = [];
			   if ($post->post_type === 'page') {
				   $urls = $this->collect_urls_for_page($post_id);
			   } else {
				   $urls = $this->collect_urls_for_post($post_id, $update);
			   }
			   // Lên lịch xử lý URL cache
			   $this->schedule_urls_processing($urls);
		   }
	   } catch (Exception $e) {
		   $this->log_message("Lỗi xử lý lưu bài viết: " . $e->getMessage());
	   }
	}

    public function handle_before_delete_post($post_id) {
        try {
            $post = get_post($post_id);
            if (!$post || !in_array($post->post_type, $this->supported_post_types)) {
                return;
            }
            $permalink = get_permalink($post_id);
            if ($permalink) {
                $this->schedule_urls_processing([$permalink]);
            }
        } catch (Exception $e) {
            $this->log_message("Lỗi xử lý xóa bài viết: " . $e->getMessage());
        }
    }

    public function handle_trash_post($post_id) {
        try {
            $post = get_post($post_id);
            if (!$post || !in_array($post->post_type, $this->supported_post_types)) {
                return;
            }
            $permalink = get_permalink($post_id);
            if ($permalink) {
                $this->schedule_urls_processing([$permalink]);
            }
        } catch (Exception $e) {
            $this->log_message("Lỗi xử lý bài viết vào thùng rác: " . $e->getMessage());
        }
    }

    public function handle_edit_term($term_id, $tt_id, $taxonomy) {
        try {
            if (in_array($taxonomy, ['category', 'post_tag'])) {
                $term_link = get_term_link($term_id, $taxonomy);
                if (!is_wp_error($term_link)) {
                    $this->schedule_urls_processing([$term_link]);
                }
            }
        } catch (Exception $e) {
            $this->log_message("Lỗi xử lý chỉnh sửa term: " . $e->getMessage());
        }
    }

    public function handle_delete_term($term_id, $tt_id, $taxonomy, $deleted_term) {
        try {
            if (in_array($taxonomy, ['category', 'post_tag'])) {
                $term_link = get_term_link($term_id, $taxonomy);
                if (!is_wp_error($term_link)) {
                    $this->schedule_urls_processing([$term_link]);
                }
            }
        } catch (Exception $e) {
            $this->log_message("Lỗi xử lý xóa term: " . $e->getMessage());
        }
    }

// Quá trình sử lý clear cache và preload cache tự động
private function schedule_urls_processing($urls) {
    if (empty($urls)) return;

    // Lọc các URL trùng lặp
    $unique_urls = array_unique($urls);

    // Thêm URLs vào queue
    $this->add_to_cache_queue($unique_urls, 'both');

    // Lên lịch xử lý queue sau 5 giây để tránh spam
    if (!wp_next_scheduled('cf_process_cache_queue')) {
        wp_schedule_single_event(time() + 5, 'cf_process_cache_queue');
    }
}

public function process_purge_urls_batch($urls) {
    try {
        $credentials = $this->get_api_credentials();
        if (empty($credentials['email']) || empty($credentials['api_key']) || empty($credentials['zone_id'])) {
            throw new Exception("Thiếu thông tin xác thực Cloudflare API");
        }

        $batches = array_chunk($urls, self::MAX_URLS_PER_BATCH);

        foreach ($batches as $batch) {
            $this->log_message("Gửi batch xóa cache: " . implode(', ', $batch));

            $response = wp_remote_post('https://api.cloudflare.com/client/v4/zones/' . $credentials['zone_id'] . '/purge_cache', [
                'headers' => [
                    'X-Auth-Email' => $credentials['email'],
                    'X-Auth-Key' => $credentials['api_key'],
                    'Content-Type' => 'application/json',
                ],
                'body' => json_encode(['files' => $batch]),
                'timeout' => 30,
                'blocking' => false // Non-blocking request
            ]);

            if (is_wp_error($response)) {
                $this->log_message("Lỗi xóa cache batch: " . $response->get_error_message());
            }
        }

        $this->log_message("Đã gửi tất cả yêu cầu xóa cache cho " . count($urls) . " URLs");
    } catch (Exception $e) {
        $this->log_message("Lỗi xóa cache: " . $e->getMessage());
        // Retry sau 30 giây
        wp_schedule_single_event(time() + 30, self::ACTION_PURGE_URLS, [$urls]);
    }
}

public function process_preload_urls_batch($urls) {
    try {
        // Giới hạn số lượng request đồng thời
        $max_concurrent_requests = 5; // Giảm xuống để tránh overload

        $this->log_message("Bắt đầu preload " . count($urls) . " URLs với " . $max_concurrent_requests . " request đồng thời");

        // Chia nhỏ danh sách URL thành các chunk
        $chunks = array_chunk($urls, $max_concurrent_requests);
        foreach ($chunks as $chunk) {
            $this->process_preload_urls_with_wp_remote($chunk);
            // Nghỉ 1 giây giữa các chunk để tránh spam
            sleep(1);
        }
    } catch (Exception $e) {
        $this->log_message("Lỗi preload cache: " . $e->getMessage());
        // Retry sau 60 giây
        wp_schedule_single_event(time() + 60, self::ACTION_PRELOAD_URLS, [$urls]);
    }
}

private function process_preload_urls_with_wp_remote($urls) {
    foreach ($urls as $url) {
        // Sử dụng wp_remote_get với non-blocking
        $response = wp_remote_get($url, [
            'timeout' => 5,
            'blocking' => false, // Non-blocking request
            'user-agent' => 'Cache Preloader',
            'sslverify' => false
        ]);

        if (is_wp_error($response)) {
            $this->log_message("Lỗi preload URL {$url}: " . $response->get_error_message());
        } else {
            $this->log_message("Đã gửi request preload: " . $url);
        }
    }
}

// Thêm method để xử lý cache queue
public function process_cache_queue() {
    $queue = get_transient('cf_cache_queue');
    if (empty($queue)) {
        return;
    }

    // Xử lý purge trước
    if (!empty($queue['purge'])) {
        $this->process_purge_urls_batch($queue['purge']);
    }

    // Sau đó preload
    if (!empty($queue['preload'])) {
        $this->process_preload_urls_batch($queue['preload']);
    }

    // Xóa queue sau khi xử lý
    delete_transient('cf_cache_queue');
}

// Method để thêm URLs vào queue
private function add_to_cache_queue($urls, $type = 'both') {
    $queue = get_transient('cf_cache_queue') ?: ['purge' => [], 'preload' => []];

    if ($type === 'both' || $type === 'purge') {
        $queue['purge'] = array_unique(array_merge($queue['purge'], $urls));
    }

    if ($type === 'both' || $type === 'preload') {
        $queue['preload'] = array_unique(array_merge($queue['preload'], $urls));
    }

    // Lưu queue với thời gian expire 10 phút
    set_transient('cf_cache_queue', $queue, 600);
}



private function collect_urls_for_page($post_id) {
    $urls = [];
    try {
        $permalink = get_permalink($post_id);
        if ($permalink) {
            $urls[] = $permalink;
        }
        $this->log_message("Đã thu thập URL cho trang: " . implode(', ', $urls));
    } catch (Exception $e) {
        $this->log_message("Lỗi thu thập URL trang: " . $e->getMessage());
    }
    return $urls;
}

private function collect_urls_for_post($post_id, $update) {
    $urls = [];
    try {
        $permalink = get_permalink($post_id);
        if ($permalink) {
            $urls[] = $permalink;
        }

        $urls[] = home_url('/');
        $this->add_paginated_urls($urls, home_url('/'), $this->get_total_pages());

        $old_categories = get_post_meta($post_id, '_old_categories', true) ?: [];
        $old_tags = get_post_meta($post_id, '_old_tags', true) ?: [];
        
        $new_category_ids = wp_get_post_categories($post_id, ['fields' => 'ids']);
        $new_tag_ids = wp_get_post_tags($post_id, ['fields' => 'ids']);
        
        $categories_to_purge = array_unique(array_merge($old_categories, $new_category_ids));
        $tags_to_purge = array_unique(array_merge($old_tags, $new_tag_ids));

        foreach ($categories_to_purge as $category_id) {
            $category_link = get_category_link($category_id);
            if ($category_link) {
                $urls[] = $category_link;
                $this->add_paginated_urls($urls, $category_link, $this->get_total_pages_in_category($category_id));
            }
        }

        foreach ($tags_to_purge as $tag_id) {
            $tag_link = get_tag_link($tag_id);
            if ($tag_link) {
                $urls[] = $tag_link;
                $this->add_paginated_urls($urls, $tag_link, $this->get_total_pages_in_tag($tag_id));
            }
        }

        if ($update) {
            update_post_meta($post_id, '_old_categories', $new_category_ids);
            update_post_meta($post_id, '_old_tags', $new_tag_ids);
        } else {
            add_post_meta($post_id, '_old_categories', $new_category_ids, true);
            add_post_meta($post_id, '_old_tags', $new_tag_ids, true);
        }

            $options = get_option('cloudflare_cache_options');
            $custom_urls = array_filter(explode("\n", $options['custom_urls'] ?? ''));
			foreach ($custom_urls as $custom_url) {
				$custom_url = trim($custom_url);
				if (preg_match('/^(https?:\/\/)/', $custom_url)) {
					// Nếu là URL đầy đủ, giữ nguyên
					$urls[] = $custom_url;
				} elseif (strpos($custom_url, '/') === 0) {
					// Nếu là đường dẫn tương đối, thêm domain
					$urls[] = home_url($custom_url);
				}
			}


        $this->log_message("Đã thu thập URL cho bài viết: " . implode(', ', $urls));
    } catch (Exception $e) {
        $this->log_message("Lỗi thu thập URL bài viết: " . $e->getMessage());
    }
    return array_unique($urls); // Đảm bảo các URL là duy nhất
}

    private function add_paginated_urls(&$urls, $base_url, $total_pages) {
        for ($i = 2; $i <= $total_pages; $i++) {
            $urls[] = trailingslashit($base_url) . "page/$i/";
        }
    }

    private function get_total_pages() {
        try {
            $total_posts = wp_count_posts()->publish;
            $posts_per_page = get_option('posts_per_page');
            return ceil($total_posts / $posts_per_page);
        } catch (Exception $e) {
            $this->log_message("Lỗi tính tổng số trang: " . $e->getMessage());
            return 1;
        }
    }

    private function get_total_pages_in_category($category_id) {
        try {
            $category = get_category($category_id);
            if (!$category || is_wp_error($category)) {
                return 0;
            }
            $total_posts = $category->count;
            $posts_per_page = get_option('posts_per_page');
            return ceil($total_posts / $posts_per_page);
        } catch (Exception $e) {
            $this->log_message("Lỗi tính tổng số trang danh mục: " . $e->getMessage());
            return 1;
        }
    }

    private function get_total_pages_in_tag($tag_id) {
        try {
            $tag = get_term($tag_id);
            if (!$tag || is_wp_error($tag)) {
                return 0;
            }
            $total_posts = $tag->count;
            $posts_per_page = get_option('posts_per_page');
            return ceil($total_posts / $posts_per_page);
        } catch (Exception $e) {
            $this->log_message("Lỗi tính tổng số trang tag: " . $e->getMessage());
            return 1;
        }
    }

    private function collect_urls_to_purge($post) {
        $urls = [];
        if ($post->post_type === 'page') {
            $urls = $this->collect_urls_for_page($post->ID);
        } else {
            $urls = $this->collect_urls_for_post($post->ID, true);
        }
        return $urls;
    }

    private function get_api_credentials() {
        $options = get_option('cloudflare_cache_options', []);
        return [
            'email' => $options['email'] ?? '',
            'api_key' => $options['api_key'] ?? '',
            'zone_id' => $options['zone_id'] ?? '',
        ];
    }
	
public function add_admin_bar_cache_buttons() {
    global $wp_admin_bar;

    // Kiểm tra quyền truy cập
    if (!current_user_can('manage_options')) {
        return;
    }

    // Nút Clear Cache
    $wp_admin_bar->add_node([
        'id' => 'cloudflare-clear-cache',
        'title' => '🗑️ Clear Cache',
        'href' => wp_nonce_url(admin_url('admin-post.php?action=cloudflare_clear_cache'), 'cloudflare_clear_cache'),
        'parent' => 'top-secondary', // Nhóm top-secondary
        'meta' => ['title' => 'Xóa toàn bộ cache Cloudflare']
    ]);

    // Nút Preload Cache
    $wp_admin_bar->add_node([
        'id' => 'cloudflare-preload-cache',
        'title' => '♻️ Preload Cache',
        'href' => wp_nonce_url(admin_url('admin-post.php?action=cloudflare_preload_cache'), 'cloudflare_preload_cache'),
        'parent' => 'top-secondary', // Nhóm top-secondary
        'meta' => ['title' => 'Preload cache từ sitemap']
    ]);

    // Nút Cache Rules
    $wp_admin_bar->add_node([
        'id' => 'cloudflare-cache-rules',
        'title' => '⚙️ Cache Rules',
        'parent' => 'top-secondary',
        'meta' => ['title' => 'Quản lý Cache Rules']
    ]);

    // Sub-menu cho Cache Rules
    $wp_admin_bar->add_node([
        'id' => 'cloudflare-create-rule',
        'title' => '➕ Tạo Cache Rule',
        'href' => wp_nonce_url(admin_url('admin-post.php?action=cloudflare_create_cache_rule'), 'cloudflare_create_cache_rule'),
        'parent' => 'cloudflare-cache-rules',
        'meta' => ['title' => 'Tạo Cache Rule tự động cho WordPress']
    ]);

    $wp_admin_bar->add_node([
        'id' => 'cloudflare-delete-rule',
        'title' => '🗑️ Xóa Cache Rule',
        'href' => wp_nonce_url(admin_url('admin-post.php?action=cloudflare_delete_cache_rule'), 'cloudflare_delete_cache_rule'),
        'parent' => 'cloudflare-cache-rules',
        'meta' => ['title' => 'Xóa Cache Rule đã tạo']
    ]);
}



    public function display_admin_notices() {
        $message = get_transient('cf_cache_message');
        if ($message) {
            delete_transient('cf_cache_message');
            $class = $message['type'] === 'error' ? 'notice-error' : ($message['type'] === 'info' ? 'notice-info' : 'notice-success');
            ?>
            <div class="notice <?php echo $class; ?> is-dismissible">
                <p><?php echo esc_html($message['message']); ?></p>
            </div>
            <?php
        }
    }

    public function handle_cloudflare_clear_cache() {
        // Kiểm tra nonce
        check_admin_referer('cloudflare_clear_cache');
        
        // Kiểm tra quyền
        if (!current_user_can('manage_options')) {
            wp_die('Bạn không có quyền thực hiện thao tác này.');
        }

        try {
            $credentials = $this->get_api_credentials();
            
            if (empty($credentials['email']) || empty($credentials['api_key']) || empty($credentials['zone_id'])) {
                throw new Exception("Thiếu thông tin xác thực Cloudflare API");
            }

            $response = wp_remote_post(
                'https://api.cloudflare.com/client/v4/zones/' . $credentials['zone_id'] . '/purge_cache',
                [
                    'headers' => [
                        'X-Auth-Email' => $credentials['email'],
                        'X-Auth-Key' => $credentials['api_key'],
                        'Content-Type' => 'application/json',
                    ],
                    'body' => json_encode(['purge_everything' => true]),
                    'timeout' => 30,
                ]
            );

            if (is_wp_error($response)) {
                throw new Exception($response->get_error_message());
            }

            $body = json_decode(wp_remote_retrieve_body($response), true);
            if (empty($body['success'])) {
                throw new Exception("API Cloudflare trả về lỗi: " . json_encode($body['errors'] ?? []));
            }

            $this->log_message("Đã xóa toàn bộ cache Cloudflare thành công");
            
            set_transient('cf_cache_message', [
                'type' => 'success', 
                'message' => 'Đã xóa toàn bộ cache Cloudflare thành công!'
            ], 30);
        } catch (Exception $e) {
            $this->log_message("Lỗi xóa toàn bộ cache: " . $e->getMessage());
            
            set_transient('cf_cache_message', [
                'type' => 'error', 
                'message' => 'Không thể xóa cache: ' . $e->getMessage()
            ], 30);
        }

    // Sử dụng JS redirect để tránh hook WordPress
    ?>
    <script>window.location.href='<?php echo wp_get_referer(); ?>';</script>
    <?php
    exit;
    }

public function handle_cloudflare_preload_cache() {
    check_admin_referer('cloudflare_preload_cache');

    if (!current_user_can('manage_options')) {
        wp_die('Bạn không có quyền thực hiện thao tác này.');
    }

    // Lên lịch preload sitemap ngay lập tức
    wp_schedule_single_event(time() + 1, 'cloudflare_preload_sitemap_action');

    set_transient('cf_cache_message', [
        'type' => 'info',
        'message' => 'Đã lên lịch preload cache từ sitemap.'
    ], 30);

    // Sử dụng JS redirect để tránh hook WordPress
    ?>
    <script>window.location.href='<?php echo wp_get_referer(); ?>';</script>
    <?php
    exit;
}

public function handle_create_cache_rule() {
    check_admin_referer('cloudflare_create_cache_rule');

    if (!current_user_can('manage_options')) {
        wp_die('Bạn không có quyền thực hiện thao tác này.');
    }

    $result = $this->create_cache_rule();

    if ($result === true) {
        set_transient('cf_cache_message', [
            'type' => 'success',
            'message' => 'Đã tạo Cache Rule thành công!'
        ], 30);
    } elseif ($result === 'exists') {
        set_transient('cf_cache_message', [
            'type' => 'info',
            'message' => 'Cache Rule đã tồn tại.'
        ], 30);
    } else {
        set_transient('cf_cache_message', [
            'type' => 'error',
            'message' => 'Không thể tạo Cache Rule. Vui lòng kiểm tra log để biết chi tiết.'
        ], 30);
    }

    ?>
    <script>window.location.href='<?php echo wp_get_referer(); ?>';</script>
    <?php
    exit;
}

public function handle_delete_cache_rule() {
    check_admin_referer('cloudflare_delete_cache_rule');

    if (!current_user_can('manage_options')) {
        wp_die('Bạn không có quyền thực hiện thao tác này.');
    }

    $result = $this->delete_cache_rule();

    if ($result === true) {
        set_transient('cf_cache_message', [
            'type' => 'success',
            'message' => 'Đã xóa Cache Rule thành công!'
        ], 30);
    } else {
        set_transient('cf_cache_message', [
            'type' => 'error',
            'message' => 'Không thể xóa Cache Rule. Vui lòng kiểm tra log để biết chi tiết.'
        ], 30);
    }

    ?>
    <script>window.location.href='<?php echo wp_get_referer(); ?>';</script>
    <?php
    exit;
}

    public function preload_sitemap_urls() {
        try {
            $sitemap_url = home_url('/sitemap.xml');
            $urls_to_preload = $this->extract_urls_from_sitemap($sitemap_url);

            if (empty($urls_to_preload)) {
                throw new Exception("Không tìm thấy URL nào trong sitemap");
            }

            $this->log_message("Đã tìm thấy " . count($urls_to_preload) . " URL để preload");

            // Chia URLs thành batch để preload
            $batches = array_chunk($urls_to_preload, 30);
            foreach ($batches as $batch) {
                $this->process_preload_urls_batch($batch);
            }

            $this->log_message("Hoàn tất preload toàn bộ URL từ sitemap");
        } catch (Exception $e) {
            $this->log_message("Lỗi preload sitemap: " . $e->getMessage());
        }
    }

    private function extract_urls_from_sitemap($sitemap_url) {
        $urls = [];
        try {
            $sitemap_content = wp_remote_get($sitemap_url);
            
            if (is_wp_error($sitemap_content)) {
                throw new Exception("Không thể tải sitemap: " . $sitemap_content->get_error_message());
            }

            $sitemap_body = wp_remote_retrieve_body($sitemap_content);
            $xml = simplexml_load_string($sitemap_body);

            if ($xml === false) {
                throw new Exception("Không thể phân tích XML sitemap");
            }

            // Xử lý sitemap gốc hoặc sitemap con
            if (isset($xml->sitemap)) {
                // Đây là sitemap index, phải đọc các sitemap con
                foreach ($xml->sitemap as $sub_sitemap) {
                    $sub_urls = $this->extract_urls_from_sitemap((string)$sub_sitemap->loc);
                    $urls = array_merge($urls, $sub_urls);
                }
            } else {
                // Đây là sitemap chứa URLs
                foreach ($xml->url as $url_entry) {
                    $urls[] = (string)$url_entry->loc;
                }
            }
        } catch (Exception $e) {
            $this->log_message("Lỗi trích xuất URL từ sitemap: " . $e->getMessage());
        }

        return $urls;
    }


	
	
	// Lên lịch cron job
public function schedule_cloudflare_preload_cache() {
    // Xóa cron job cũ nếu tồn tại
    if (wp_next_scheduled('cloudflare_daily_preload_cache')) {
        wp_clear_scheduled_hook('cloudflare_daily_preload_cache');
    }

    // Lấy cài đặt hiện tại
    $options = get_option('cloudflare_cache_options');

    // Kiểm tra nếu Auto Preload được bật
    if (!empty($options['auto_preload']) && $options['auto_preload'] === '1') {
        $preload_time = isset($options['auto_preload_time']) ? $options['auto_preload_time'] : '02:00'; // Mặc định 02:00
        $timezone = wp_timezone();

        // Tạo đối tượng DateTime với múi giờ của WordPress
        $scheduled_time = new DateTime('today ' . $preload_time, $timezone);

        // Kiểm tra nếu thời gian đã qua, đặt lịch cho ngày mai
        if ($scheduled_time->getTimestamp() < time()) {
            $scheduled_time->modify('tomorrow ' . $preload_time);
        }

        // Lấy timestamp để lên lịch cron job
        $scheduled_timestamp = $scheduled_time->getTimestamp();

        // Chỉ đặt cron nếu chưa có
        if (!wp_next_scheduled('cloudflare_daily_preload_cache')) {
            wp_schedule_event($scheduled_timestamp, 'daily', 'cloudflare_daily_preload_cache');
            $this->log_message("Đã lên lịch cron job tại: " . $scheduled_time->format('Y-m-d H:i:s'));
        } else {
            $this->log_message("Cron job đã được lên lịch trước đó.");
        }
    }
}

    // Xử lý cron job
public function handle_cloudflare_daily_preload_cache() {
    // Lấy cài đặt hiện tại
    $options = get_option('cloudflare_cache_options');

    // Kiểm tra xem cron đã chạy hôm nay chưa, tránh chạy lặp
    if (!empty($options['last_preload_run']) && $options['last_preload_run'] === date('Y-m-d')) {
        $this->log_message("Cron preload cache đã chạy hôm nay, bỏ qua lần chạy này.");
        return;
    }

    // Đánh dấu đã chạy cron hôm nay
    $options['last_preload_run'] = date('Y-m-d');
    update_option('cloudflare_cache_options', $options);

    $this->log_message("Bắt đầu chạy preload sitemap theo lịch");

    // Lên lịch preload sitemap
    wp_schedule_single_event(time() + 1, 'cloudflare_preload_sitemap_action');

    set_transient('cf_cache_message', [
        'type' => 'info',
        'message' => 'Đã lên lịch preload cache tự động. Vui lòng kiểm tra nhật ký để biết chi tiết.'
    ], 30);
}
	

    // Cập nhật cron job khi thay đổi thời gian
public function update_cloudflare_preload_cron($old_value, $new_value) {
    // Kiểm tra nếu auto_preload hoặc auto_preload_time thay đổi
    if ($old_value['auto_preload'] !== $new_value['auto_preload'] || $old_value['auto_preload_time'] !== $new_value['auto_preload_time']) {
        if ($new_value['auto_preload'] === '1') {
            wp_clear_scheduled_hook('cloudflare_daily_preload_cache');
            $preload_time = isset($new_value['auto_preload_time']) ? $new_value['auto_preload_time'] : '02:00'; // Mặc định 02:00
            $timezone = wp_timezone();

            // Tạo đối tượng DateTime với múi giờ của WordPress
            $scheduled_time = new DateTime('today ' . $preload_time, $timezone);

            // Kiểm tra nếu thời gian đã qua, đặt lịch cho ngày mai
            if ($scheduled_time->getTimestamp() < time()) {
                $scheduled_time->modify('tomorrow ' . $preload_time);
            }

            // Lấy timestamp để lên lịch cron job
            $scheduled_timestamp = $scheduled_time->getTimestamp();

            // Đặt lại cron job
            wp_schedule_event($scheduled_timestamp, 'daily', 'cloudflare_daily_preload_cache');
            $this->log_message("Đã cập nhật cron job tại: " . $scheduled_time->format('Y-m-d H:i:s'));
        } else {
            wp_clear_scheduled_hook('cloudflare_daily_preload_cache');
            $this->log_message("Auto Preload không được bật, đã xóa cron job.");
        }
    }
}

    // Xóa cron job khi plugin bị vô hiệu hóa
    public function deactivate_cloudflare_preload_cache() {
        wp_clear_scheduled_hook('cloudflare_daily_preload_cache');
    }
	
	
	###########GIAO DIỆn ##############
	
 // Thêm menu admin
    public function add_admin_menu() {
        add_management_page(
            'Cloudflare Auto Cache Purge And Preload', // Tiêu đề trang
            'Cloudflare Auto Cache Purge And Preload', // Tiêu đề menu
            'manage_options', // Quyền truy cập
            'cloudflare-cache-settings', // Slug menu
            [$this, 'render_settings_page'] // Callback hiển thị trang
        );
    }
    public function add_settings_link($links) {
        $settings_link = '<a href="' . admin_url('tools.php?page=cloudflare-cache-settings') . '">Settings</a>';
        array_unshift($links, $settings_link);
        return $links;
    }	

    // Đăng ký settings
    public function register_settings() {
        register_setting('cloudflare_cache_settings', 'cloudflare_cache_options', [
            'sanitize_callback' => [$this, 'sanitize_settings']
        ]);

        // Section API Cloudflare
        add_settings_section(
            'cloudflare_api_settings',
            'Cài đặt Cloudflare API',
            [$this, 'render_section_api'],
            'cloudflare-cache-settings'
        );

        // Các field API
        add_settings_field(
            'cloudflare_email',
            'Cloudflare API Email',
            [$this, 'render_email_field'],
            'cloudflare-cache-settings',
            'cloudflare_api_settings'
        );
        add_settings_field(
            'cloudflare_api_key',
            'Cloudflare API Key',
            [$this, 'render_api_key_field'],
            'cloudflare-cache-settings',
            'cloudflare_api_settings'
        );
        add_settings_field(
            'cloudflare_zone_id',
            'Cloudflare Zone ID',
            [$this, 'render_zone_id_field'],
            'cloudflare-cache-settings',
            'cloudflare_api_settings'
        );

        // Section URL Settings
        add_settings_section(
            'url_settings',
            'Cài đặt URL',
            [$this, 'render_section_url'],
            'cloudflare-cache-settings'
        );
        add_settings_field(
            'sitemap_url',
            'Sitemap URL',
            [$this, 'render_sitemap_field'],
            'cloudflare-cache-settings',
            'url_settings'
        );
        add_settings_field(
            'custom_urls',
            'Custom URLs để Purge',
            [$this, 'render_custom_urls_field'],
            'cloudflare-cache-settings',
            'url_settings'
        );

        // Section Other Settings
        add_settings_section(
            'other_settings',
            'Cài đặt khác',
            [$this, 'render_section_other'],
            'cloudflare-cache-settings'
        );
        add_settings_field(
            'enable_logging',
            'Logging',
            [$this, 'render_logging_field'],
            'cloudflare-cache-settings',
            'other_settings'
        );
		        // Field Auto Preload
        add_settings_field(
            'auto_preload',
            'Auto Preload',
            [$this, 'render_auto_preload_field'],
            'cloudflare-cache-settings',
            'other_settings'
        );
        add_settings_field(
            'auto_preload_time',
            'Thời gian chạy Auto Preload',
            [$this, 'render_auto_preload_time_field'],
            'cloudflare-cache-settings',
            'other_settings'
        );

        // Section Cache Rules
        add_settings_section(
            'cache_rules_settings',
            'Quản lý Cache Rules',
            [$this, 'render_section_cache_rules'],
            'cloudflare-cache-settings'
        );

        add_settings_field(
            'cache_rules_info',
            'Cache Rules Status',
            [$this, 'render_cache_rules_info_field'],
            'cloudflare-cache-settings',
            'cache_rules_settings'
        );
    }

    // Render trang settings
public function render_settings_page() {
    ?>
    <div class="wrap">
        <h1>Cài đặt Cloudflare Auto Cache Purge And Preload</h1>

            <!-- Hiển thị các thông báo lỗi nếu có -->
        <?php settings_errors('cloudflare_cache_settings'); ?>

        <form action="options.php" method="post">
            <?php
            settings_fields('cloudflare_cache_settings');
            do_settings_sections('cloudflare-cache-settings');
            submit_button('Lưu cài đặt');
            ?>
        </form>

        <hr>
        <h2>Reset về mặc định</h2>
        <p>Nhấn nút dưới đây để khôi phục về cài đặt mặc định.</p>

        <!-- Form reset -->
        <form action="" method="post">
            <?php
            $nonce = wp_create_nonce('cloudflare_reset_settings');
            ?>
            <input type="hidden" name="action" value="reset_cloudflare_settings">
            <input type="hidden" name="_wpnonce" value="<?php echo $nonce; ?>">
            <button type="submit" class="button button-secondary">
                Reset về mặc định
            </button>
        </form>
    </div>

    <!-- JavaScript để xóa tham số settings-reset khỏi URL -->
    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has('settings-reset')) {
                urlParams.delete('settings-reset');
                const newUrl = window.location.pathname + '?' + urlParams.toString();
                window.history.replaceState({}, '', newUrl);
            }
        });
    </script>
    <!-- JavaScript để toggle hiển thị trường thời gian -->
    <script type="text/javascript">
    jQuery(document).ready(function($) {
        // Lấy các phần tử liên quan
        const autoPreloadCheckbox = $('#auto_preload');
        const autoPreloadTimeField = $('#auto_preload_time').closest('tr');

        // Hàm kiểm tra trạng thái checkbox
        function toggleAutoPreloadTimeField() {
            if (autoPreloadCheckbox.is(':checked')) {
                autoPreloadTimeField.show();
            } else {
                autoPreloadTimeField.hide();
            }
        }

        // Khởi tạo trạng thái ban đầu
        toggleAutoPreloadTimeField();

        // Sự kiện khi checkbox thay đổi
        autoPreloadCheckbox.on('change', function() {
            toggleAutoPreloadTimeField();
        });
    });
    </script>

    <?php
}


    // Render các section
    public function render_section_api() {
        echo '<p>Nhập thông tin xác thực Cloudflare API của bạn:</p>';
    }

    public function render_section_url() {
        echo '<p>Cấu hình các URL cần xử lý:</p>';
    }

    public function render_section_other() {
        echo '<p>Các cài đặt khác:</p>';
    }

    public function render_section_cache_rules() {
        echo '<p>Tạo và quản lý Cache Rules tự động cho WordPress:</p>';
    }

    // Render các field
    public function render_email_field() {
        $options = get_option('cloudflare_cache_options');
        ?>
        <input type="email" id="cloudflare_email" name="cloudflare_cache_options[email]"
               value="<?php echo esc_attr($options['email'] ?? ''); ?>" class="regular-text" required>
        <?php
    }

    public function render_api_key_field() {
        $options = get_option('cloudflare_cache_options');
        ?>
        <input type="password" id="cloudflare_api_key" name="cloudflare_cache_options[api_key]"
               value="<?php echo esc_attr($options['api_key'] ?? ''); ?>" class="regular-text" required>
        <?php
    }

    public function render_zone_id_field() {
        $options = get_option('cloudflare_cache_options');
        ?>
        <input type="text" id="cloudflare_zone_id" name="cloudflare_cache_options[zone_id]"
               value="<?php echo esc_attr($options['zone_id'] ?? ''); ?>" class="regular-text" required>
        <?php
    }

    public function render_sitemap_field() {
        $options = get_option('cloudflare_cache_options');
        ?>
        <input type="url" id="sitemap_url" name="cloudflare_cache_options[sitemap_url]"
               value="<?php echo esc_url($options['sitemap_url'] ?? home_url('/sitemap.xml')); ?>" class="regular-text">
        <?php
    }

    public function render_custom_urls_field() {
        $options = get_option('cloudflare_cache_options');
        ?>
        <textarea id="custom_urls" name="cloudflare_cache_options[custom_urls]" rows="5" cols="50" class="large-text"><?php
            echo esc_textarea($options['custom_urls'] ?? '');
        ?></textarea>
        <p class="description">Nhập mỗi URL trên một dòng, sử dụng đường dẫn tương đối (ví dụ: /archives/) hoặc URL tuyệt đối (ví dụ: <?php echo home_url('/archives/'); ?>)</p>
        <?php
    }

// Render Logging Field
public function render_logging_field() {
    $options = get_option('cloudflare_cache_options');
    ?>
    <input type="checkbox" id="enable_logging" name="cloudflare_cache_options[enable_logging]"
           value="1" <?php checked(($options['enable_logging'] ?? '0'), '1'); ?>>
    <?php
}

// Render Auto Preload Field
public function render_auto_preload_field() {
    $options = get_option('cloudflare_cache_options');
    ?>
    <input type="checkbox" id="auto_preload" name="cloudflare_cache_options[auto_preload]"
           value="1" <?php checked(($options['auto_preload'] ?? '0'), '1'); ?>>
    <?php
}
// Render Auto Preload Time Field
public function render_auto_preload_time_field() {
    $options = get_option('cloudflare_cache_options');
    $selected_time = $options['auto_preload_time'] ?? '02:00'; // Giá trị mặc định là 02:00
    ?>
    <select id="auto_preload_time" name="cloudflare_cache_options[auto_preload_time]">
        <?php for ($hour = 0; $hour <= 23; $hour++): ?>
            <option value="<?php echo sprintf('%02d:00', $hour); ?>" <?php selected($selected_time, sprintf('%02d:00', $hour)); ?>>
                <?php echo sprintf('%02d:00', $hour); ?>
            </option>
        <?php endfor; ?>
    </select>
    <p class="description">Chọn giờ để chạy Auto Preload (theo múi giờ WordPress).</p>
    <?php
}

// Render Cache Rules Info Field
public function render_cache_rules_info_field() {
    $rule_url = get_option('cf_cloudflare_rule_url');
    ?>
    <div style="background: #f9f9f9; padding: 15px; border-left: 4px solid #0073aa; margin: 10px 0;">
        <h4 style="margin-top: 0;">🚀 Cache Rules cho WordPress</h4>
        <p>Cache Rules giúp tối ưu hiệu suất website bằng cách tự động cache các trang phù hợp và loại trừ các trang admin, login, checkout...</p>

        <?php if ($rule_url): ?>
            <div style="background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0;">
                <strong>✅ Cache Rule đã được tạo!</strong><br>
                <a href="<?php echo esc_url($rule_url); ?>" target="_blank" class="button button-secondary">
                    🔗 Xem trên Cloudflare Dashboard
                </a>
            </div>
        <?php else: ?>
            <div style="background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; border-radius: 4px; margin: 10px 0;">
                <strong>⚠️ Chưa có Cache Rule</strong><br>
                Nhấn nút "Tạo Cache Rule" ở admin bar để tạo rule tự động.
            </div>
        <?php endif; ?>

        <div style="margin-top: 15px;">
            <h5>📋 Rule sẽ cache:</h5>
            <ul style="margin-left: 20px;">
                <li>✅ Tất cả trang công khai (posts, pages, archives)</li>
                <li>✅ Trang chủ và các trang danh mục</li>
                <li>✅ CSS, JS, images và static files</li>
            </ul>

            <h5>🚫 Rule sẽ loại trừ:</h5>
            <ul style="margin-left: 20px;">
                <li>❌ Trang admin (/wp-admin, /wp-login)</li>
                <li>❌ API endpoints (/wp-json, /wc-api)</li>
                <li>❌ Trang checkout, cart, account</li>
                <li>❌ Users đã đăng nhập</li>
                <li>❌ Trang có query parameters đặc biệt</li>
            </ul>
        </div>

        <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #ddd;">
            <strong>💡 Lưu ý:</strong> Cache Rule sẽ tự động tắt các Page Rules có "Cache Everything" để tránh conflict.
        </div>
    </div>
    <?php
}

public function sanitize_settings($input, $is_reset = false) {
    $sanitized = [];
	
	    // Kiểm tra nếu đang thực hiện reset
    if (isset($_POST['action']) && $_POST['action'] === 'reset_cloudflare_settings') {
        return $input; // Bỏ qua kiểm tra API khi reset
    }

    // Email Cloudflare
    $sanitized['email'] = isset($input['email']) ? sanitize_email($input['email']) : '';

    // API Key
    $sanitized['api_key'] = isset($input['api_key']) ? sanitize_text_field($input['api_key']) : '';

    // Zone ID
    $sanitized['zone_id'] = isset($input['zone_id']) ? sanitize_text_field($input['zone_id']) : '';

    // Nếu không phải đang reset, kiểm tra tính hợp lệ của API
    if (!$is_reset) {
        // Kiểm tra nếu thiếu một trong ba giá trị quan trọng
        if (empty($sanitized['email']) || empty($sanitized['api_key']) || empty($sanitized['zone_id'])) {
            add_settings_error(
                'cloudflare_cache_settings',
                'cloudflare_cache_settings_error',
                'Vui lòng nhập đầy đủ Email, API Key và Zone ID.',
                'error'
            );
            return get_option('cloudflare_cache_options'); // Trả về giá trị cũ để tránh mất dữ liệu
        }

        // Kiểm tra tính hợp lệ của API credentials
        $validation_result = $this->validate_api_credentials($sanitized['email'], $sanitized['api_key'], $sanitized['zone_id']);
        if (!$validation_result['success']) {
            add_settings_error(
                'cloudflare_cache_settings',
                'cloudflare_cache_settings_error',
                'Thông tin Cloudflare không hợp lệ: ' . $validation_result['message'],
                'error'
            );
            return get_option('cloudflare_cache_options');
        }
		
    }

// Tự động phát hiện sitemap nếu không nhập
    $sanitized['sitemap_url'] = !empty($input['sitemap_url']) ? esc_url_raw($input['sitemap_url']) : $this->detect_sitemap_url();
	
	    // Custom URLs
    $sanitized['custom_urls'] = isset($input['custom_urls']) ? sanitize_textarea_field($input['custom_urls']) : '';

    // Enable Logging (Checkbox) - FIXED
    $sanitized['enable_logging'] = !empty($input['enable_logging']) ? '1' : '0';

    // Auto Preload (Checkbox) - FIXED
    $sanitized['auto_preload'] = !empty($input['auto_preload']) ? '1' : '0';

    // Auto Preload Time (Dropdown) - FIXED
    if (isset($input['auto_preload_time']) && preg_match('/^\d{2}:\d{2}$/', $input['auto_preload_time'])) {
        $sanitized['auto_preload_time'] = sanitize_text_field($input['auto_preload_time']);
    } else {
        $sanitized['auto_preload_time'] = '02:00'; // Giá trị mặc định nếu không hợp lệ
    }

    return $sanitized;
}

/**
 * Validate Cloudflare API credentials
 */
private function validate_api_credentials($email, $api_key, $zone_id) {
    $response = wp_remote_get('https://api.cloudflare.com/client/v4/zones/' . $zone_id, [
        'headers' => [
            'X-Auth-Email' => $email,
            'X-Auth-Key' => $api_key,
            'Content-Type' => 'application/json',
        ],
        'timeout' => 30,
    ]);

    if (is_wp_error($response)) {
        return ['success' => false, 'message' => 'Lỗi kết nối tới Cloudflare: ' . $response->get_error_message()];
    }

    $body = json_decode(wp_remote_retrieve_body($response), true);

    if (!empty($body['success']) && $body['success'] === true) {
        return ['success' => true, 'message' => 'Thông tin API hợp lệ.'];
    } else {
        // Kiểm tra các lỗi cụ thể từ Cloudflare
        $errors = !empty($body['errors']) ? array_map(function ($error) {
            // Kiểm tra lỗi Zone ID không hợp lệ
            if (strpos($error['message'], 'Could not route to') !== false) {
                return 'Zone ID không hợp lệ. Vui lòng kiểm tra lại Zone ID của bạn.';
            }
            // Kiểm tra lỗi API Key hoặc Email không hợp lệ
            if (strpos($error['message'], 'Unknown X-Auth-Key or X-Auth-Email') !== false) {
                return 'API Key hoặc Email không hợp lệ. Vui lòng kiểm tra lại thông tin đăng nhập của bạn.';
            }
            // Trả về thông báo lỗi mặc định nếu không phải là lỗi Zone ID hoặc API Key/Email
            return $error['message'];
        }, $body['errors']) : ['Lỗi không xác định'];
        
        return ['success' => false, 'message' => implode('; ', $errors)];
    }
}

/**
 * Tự động phát hiện Sitemap từ mã nguồn trang chủ
 */
private function detect_sitemap_url() {
    // 1. Kiểm tra các sitemap phổ biến
$possible_sitemaps = [
    '/sitemap.xml', // Mặc định của nhiều plugin
    '/sitemap_index.xml', // Yoast SEO
    '/sitemap/sitemap.xml', // Rank Math SEO
    '/sitemaps/sitemap.xml', // Một số plugin khác
    '/sitemap.php', // Phiên bản cũ hoặc plugin tùy chỉnh
    '/sitemap.txt', // Phiên bản sitemap dạng văn bản
    '/wp-sitemap.xml', // Sitemap mặc định từ WordPress 5.5+
    '/sitemap.xml.gz', // Sitemap dạng nén (Google XML Sitemaps)
    '/sitemap-main.xml', // All in One SEO Pack
    '/sitemap_index.xml.gz', // Phiên bản nén của Yoast SEO
    '/sitemap-index.xml', // SEOPress
    '/sitemap-news.xml', // Sitemap tin tức (Yoast, Rank Math)
    '/sitemap-video.xml', // Sitemap video (Yoast, Rank Math)
    '/sitemap-image.xml', // Sitemap hình ảnh (Yoast, Rank Math)
];

    foreach ($possible_sitemaps as $sitemap) {
        $url = home_url($sitemap);
        $response = wp_remote_head($url, ['timeout' => 5]);

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) == 200) {
            return esc_url_raw($url);
        }
    }

    // 2. Kiểm tra Sitemap trong mã nguồn trang chủ
    $home_url = home_url('/');
    $response = wp_remote_get($home_url, ['timeout' => 5]);

    if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) == 200) {
        $html = wp_remote_retrieve_body($response);

        // Tìm thẻ <link rel="sitemap" href="...">
        if (preg_match('/<link[^>]+rel=["\']sitemap["\'][^>]+href=["\']([^"\']+)["\']/i', $html, $matches)) {
            return esc_url_raw($matches[1]);
        }

        // Tìm sitemap xuất hiện trong thẻ <loc> của XML
        if (preg_match('/<loc>(https?:\/\/[^<]+\/sitemap[^<]*)<\/loc>/i', $html, $matches)) {
            return esc_url_raw($matches[1]);
        }
    }

    // 3. Nếu không tìm thấy, trả về sitemap mặc định
    return home_url('/sitemap.xml');
}

// Tạo Cache Rule
public function create_cache_rule() {
    $credentials = $this->get_api_credentials();
    if (empty($credentials['email']) || empty($credentials['api_key']) || empty($credentials['zone_id'])) {
        $this->log_message('ACCR: Thiếu thông tin cấu hình');
        return false;
    }

    $api_token = $credentials['api_key'];
    $email = $credentials['email'];
    $zone_id = $credentials['zone_id'];

    // Kiểm tra và tắt Page Rule có Cache Level: Cache Everything nếu có
    $page_rule_disabled = $this->disable_cache_everything_page_rule($api_token, $email, $zone_id);
    if ($page_rule_disabled) {
        $this->log_message('Phát hiện Page Rule có Cache Level: Cache Everything. Đã tắt tính năng này.');
    }

    // Tiếp tục tạo Cache Rule mới
    $this->log_message('ACCR: Bắt đầu tạo cache rule');
    $this->log_message('ACCR: Zone ID: ' . $zone_id);

    // Lấy danh sách rulesets hiện có
    $list_url = "https://api.cloudflare.com/client/v4/zones/{$zone_id}/rulesets?phase=http_request_cache_settings";
    $this->log_message('ACCR: URL lấy danh sách: ' . $list_url);

    $list_response = wp_remote_get($list_url, [
        'headers' => [
            'X-Auth-Email' => $email,
            'X-Auth-Key' => $api_token,
            'Content-Type' => 'application/json'
        ]
    ]);

    if (is_wp_error($list_response)) {
        $this->log_message('ACCR: Lỗi khi lấy danh sách ruleset - ' . $list_response->get_error_message());
        return false;
    }

    $list_body = json_decode(wp_remote_retrieve_body($list_response), true);
    $this->log_message('ACCR: Phản hồi lấy danh sách ruleset: ' . json_encode($list_body));

    if (!($list_body['success'] ?? false)) {
        $this->log_message('ACCR: Không thể lấy danh sách rulesets');
        return false;
    }

    // Tìm ruleset phù hợp
    $ruleset_id = null;
    foreach ($list_body['result'] as $ruleset) {
        if ($ruleset['phase'] === 'http_request_cache_settings') {
            $ruleset_id = $ruleset['id'];
            break;
        }
    }

    // Nếu không tìm thấy ruleset, tạo mới
    if (!$ruleset_id) {
        $this->log_message('ACCR: Không tìm thấy ruleset phù hợp, đang tạo ruleset mới');

        $create_ruleset_url = "https://api.cloudflare.com/client/v4/zones/{$zone_id}/rulesets";
        $create_ruleset_data = [
            'name' => 'Custom Cache Ruleset',
            'description' => 'Ruleset for managing cache settings',
            'kind' => 'zone',
            'phase' => 'http_request_cache_settings',
        ];

        $create_response = wp_remote_post($create_ruleset_url, [
            'headers' => [
                'X-Auth-Email' => $email,
                'X-Auth-Key' => $api_token,
                'Content-Type' => 'application/json'
            ],
            'body' => json_encode($create_ruleset_data),
            'timeout' => 30
        ]);

        if (is_wp_error($create_response)) {
            $this->log_message('ACCR: Lỗi khi tạo ruleset - ' . $create_response->get_error_message());
            return false;
        }

        $create_body = json_decode(wp_remote_retrieve_body($create_response), true);
        $this->log_message('ACCR: Phản hồi tạo ruleset: ' . json_encode($create_body));

        if (!($create_body['success'] ?? false)) {
            $errors = json_encode($create_body['errors'] ?? 'Lỗi không xác định');
            $this->log_message('ACCR: Lỗi API khi tạo ruleset: ' . $errors);
            return false;
        }

        $ruleset_id = $create_body['result']['id'] ?? null;
        if (!$ruleset_id) {
            $this->log_message('ACCR: Không thể lấy ID của ruleset mới');
            return false;
        }

        $this->log_message('ACCR: Đã tạo ruleset mới với ID: ' . $ruleset_id);
    } else {
        $this->log_message('ACCR: Đã tìm thấy ruleset ID: ' . $ruleset_id);
    }

    // Lấy danh sách rules hiện có trong ruleset
    $ruleset_url = "https://api.cloudflare.com/client/v4/zones/{$zone_id}/rulesets/{$ruleset_id}";
    $ruleset_response = wp_remote_get($ruleset_url, [
        'headers' => [
            'X-Auth-Email' => $email,
            'X-Auth-Key' => $api_token,
            'Content-Type' => 'application/json'
        ]
    ]);

    if (is_wp_error($ruleset_response)) {
        $this->log_message('ACCR: Lỗi khi lấy danh sách rules - ' . $ruleset_response->get_error_message());
        return false;
    }

    $ruleset_body = json_decode(wp_remote_retrieve_body($ruleset_response), true);
    $this->log_message('ACCR: Phản hồi lấy danh sách rules: ' . json_encode($ruleset_body));

    if (!($ruleset_body['success'] ?? false)) {
        $this->log_message('ACCR: Không thể lấy danh sách rules');
        return false;
    }

    // Lấy danh sách rules hiện tại
    $existing_rules = $ruleset_body['result']['rules'] ?? [];

    // Kiểm tra xem rule đã tồn tại chưa
    $current_domain = parse_url(home_url(), PHP_URL_HOST);
    $rule_description = "WordPress Cache Rule - {$current_domain}";
    $rule_exists = false;
    $rule_id = null;

    foreach ($existing_rules as $rule) {
        if ($rule['description'] === $rule_description) {
            $rule_exists = true;
            $rule_id = $rule['id'];
            break;
        }
    }

    if ($rule_exists) {
        $this->log_message('ACCR: Rule đã tồn tại');
        $cloudflare_dashboard_url = "https://dash.cloudflare.com/{$zone_id}/" . parse_url(home_url(), PHP_URL_HOST) . "/caching/cache-rules/{$rule_id}";
        update_option('cf_cloudflare_rule_url', $cloudflare_dashboard_url);
        return 'exists';
    }

    // Tiếp tục với việc tạo rule mới...
    return $this->create_new_cache_rule($zone_id, $ruleset_id, $existing_rules, $rule_description, $api_token, $email);
}

private function create_new_cache_rule($zone_id, $ruleset_id, $existing_rules, $rule_description, $api_token, $email) {
    // Các pattern loại trừ cho WordPress
    $exclude_patterns = [
        'starts_with(http.request.uri.path, "/wp-admin")',
        'starts_with(http.request.uri.path, "/wp-login")',
        'starts_with(http.request.uri.path, "/wp-json/")',
        'starts_with(http.request.uri.path, "/wc-api/")',
        'starts_with(http.request.uri.path, "/edd-api/")',
        'starts_with(http.request.uri.path, "/mepr/")',
        'http.request.uri.path contains "/register/"',
        'http.request.uri.path contains "/dashboard/"',
        'http.request.uri.path contains "/members-area/"',
        'http.request.uri.path contains "/wishlist-member/"',
        'http.request.uri.path contains "phs_downloads-mbr"',
        'http.request.uri.path contains "/checkout/"',
        'http.request.uri.path contains ".xsl"',
        'http.request.uri.path contains ".xml"',
        'http.request.uri.path contains ".php"',
        'starts_with(http.request.uri.query, "s=")',
        'starts_with(http.request.uri.query, "p=")',
        'http.request.uri.query contains "nocache"',
        'http.request.uri.query contains "nowprocket"',
        'http.cookie contains "wordpress_logged_in_"',
        'http.cookie contains "comment_"',
        'http.cookie contains "woocommerce_"',
        'http.cookie contains "wordpressuser_"',
        'http.cookie contains "wordpresspass_"',
        'http.cookie contains "wordpress_sec_"',
        'http.cookie contains "yith_wcwl_products"',
        'http.cookie contains "edd_items_in_cart"',
        'http.cookie contains "it_exchange_session_"',
        'http.cookie contains "comment_author"',
        'http.cookie contains "dshack_level"',
        'http.cookie contains "auth_"',
        'http.cookie contains "noaffiliate_"',
        'http.cookie contains "mp_session"',
        'http.cookie contains "xf_"',
        'http.cookie contains "mp_globalcart_"',
        'http.cookie contains "wp-resetpass-"',
        'http.cookie contains "upsell_customer"',
        'http.cookie contains "wlmapi"',
        'http.cookie contains "wishlist_reg"'
    ];

    // Kết hợp các điều kiện loại trừ
    $exclusion_expression = implode(' and not ', $exclude_patterns);

    // Lấy domain hiện tại
    $current_domain = parse_url(home_url(), PHP_URL_HOST);

    // Tạo biểu thức rule
    $rule_expression = "(http.host eq \"{$current_domain}\" and not {$exclusion_expression})";

    // Tạo rule mới
    $new_rule = [
        'expression' => $rule_expression,
        'description' => $rule_description,
        'action' => 'set_cache_settings',
        'action_parameters' => [
            'cache' => true,
            'edge_ttl' => [
                'mode' => 'override_origin',
                'default' => 31536000 // 1 năm
            ],
            'cache_key' => [
                'cache_deception_armor' => true
            ]
        ]
    ];

    // Thêm rule mới vào danh sách rules hiện có
    $existing_rules[] = $new_rule;

    // Cấu hình Cache Rule
    $data = [
        'rules' => $existing_rules
    ];

    $url = "https://api.cloudflare.com/client/v4/zones/{$zone_id}/rulesets/{$ruleset_id}";

    $this->log_message('ACCR: Đang cập nhật ruleset');
    $this->log_message('ACCR: URL cập nhật: ' . $url);

    $response = wp_remote_request($url, [
        'method' => 'PUT',
        'headers' => [
            'X-Auth-Email' => $email,
            'X-Auth-Key' => $api_token,
            'Content-Type' => 'application/json'
        ],
        'body' => json_encode($data),
        'timeout' => 30
    ]);

    if (is_wp_error($response)) {
        $this->log_message('ACCR: Lỗi khi cập nhật ruleset - ' . $response->get_error_message());
        return false;
    }

    $body = json_decode(wp_remote_retrieve_body($response), true);
    $this->log_message('ACCR: Phản hồi cập nhật ruleset: ' . json_encode($body));

    if (!($body['success'] ?? false)) {
        $errors = json_encode($body['errors'] ?? 'Lỗi không xác định');
        $this->log_message('ACCR: Lỗi API khi cập nhật ruleset: ' . $errors);
        return false;
    }

    // Sau khi tạo rule thành công, lưu URL vào database
    $rule_id = $body['result']['rules'][count($body['result']['rules']) - 1]['id'] ?? null;
    if ($rule_id) {
        // Lấy Account ID
        $account_id = $this->get_account_id($api_token, $email, $zone_id);
        if (!$account_id) {
            $this->log_message('ACCR: Không thể lấy Account ID');
            return false;
        }

        // Lấy zone domain từ API Cloudflare thay vì dùng current domain
        $zone_url = "https://api.cloudflare.com/client/v4/zones/{$zone_id}";
        $zone_response = wp_remote_get($zone_url, [
            'headers' => [
                'X-Auth-Email' => $email,
                'X-Auth-Key' => $api_token,
                'Content-Type' => 'application/json'
            ]
        ]);

        if (!is_wp_error($zone_response)) {
            $zone_body = json_decode(wp_remote_retrieve_body($zone_response), true);
            if (($zone_body['success'] ?? false) && !empty($zone_body['result']['name'])) {
                $zone_domain = $zone_body['result']['name'];
                // Tạo URL với zone domain thay vì current domain
                $cloudflare_dashboard_url = "https://dash.cloudflare.com/{$account_id}/{$zone_domain}/caching/cache-rules/{$rule_id}";
                update_option('cf_cloudflare_rule_url', $cloudflare_dashboard_url);
                $this->log_message('ACCR: URL trực tiếp đến rule: ' . $cloudflare_dashboard_url);
            } else {
                $this->log_message('ACCR: Không thể lấy zone domain từ API');
            }
        } else {
            $this->log_message('ACCR: Lỗi khi lấy zone domain - ' . $zone_response->get_error_message());
        }
    } else {
        $this->log_message('ACCR: Không thể lấy rule_id từ phản hồi API');
    }
    return true;
}

// Xóa Cache Rule
public function delete_cache_rule() {
    $credentials = $this->get_api_credentials();
    if (empty($credentials['email']) || empty($credentials['api_key']) || empty($credentials['zone_id'])) {
        $this->log_message('ACCR: Thiếu thông tin cấu hình');
        return false;
    }

    $api_token = $credentials['api_key'];
    $email = $credentials['email'];
    $zone_id = $credentials['zone_id'];

    $this->log_message('ACCR: Bắt đầu xóa cache rule');
    $this->log_message('ACCR: Zone ID: ' . $zone_id);

    // Lấy danh sách rulesets hiện có
    $list_url = "https://api.cloudflare.com/client/v4/zones/{$zone_id}/rulesets?phase=http_request_cache_settings";
    $this->log_message('ACCR: URL lấy danh sách: ' . $list_url);

    $list_response = wp_remote_get($list_url, [
        'headers' => [
            'X-Auth-Email' => $email,
            'X-Auth-Key' => $api_token,
            'Content-Type' => 'application/json'
        ]
    ]);

    if (is_wp_error($list_response)) {
        $this->log_message('ACCR: Lỗi khi lấy danh sách ruleset - ' . $list_response->get_error_message());
        return false;
    }

    $list_body = json_decode(wp_remote_retrieve_body($list_response), true);
    $this->log_message('ACCR: Phản hồi lấy danh sách ruleset: ' . json_encode($list_body));

    if (!($list_body['success'] ?? false)) {
        $this->log_message('ACCR: Không thể lấy danh sách rulesets');
        return false;
    }

    // Tìm ruleset phù hợp
    $ruleset_id = null;
    foreach ($list_body['result'] as $ruleset) {
        if ($ruleset['phase'] === 'http_request_cache_settings') {
            $ruleset_id = $ruleset['id'];
            break;
        }
    }

    if (!$ruleset_id) {
        $this->log_message('ACCR: Không tìm thấy ruleset phù hợp');
        return false;
    }

    $this->log_message('ACCR: Đã tìm thấy ruleset ID: ' . $ruleset_id);

    // Lấy danh sách rules hiện có trong ruleset
    $ruleset_url = "https://api.cloudflare.com/client/v4/zones/{$zone_id}/rulesets/{$ruleset_id}";
    $ruleset_response = wp_remote_get($ruleset_url, [
        'headers' => [
            'X-Auth-Email' => $email,
            'X-Auth-Key' => $api_token,
            'Content-Type' => 'application/json'
        ]
    ]);

    if (is_wp_error($ruleset_response)) {
        $this->log_message('ACCR: Lỗi khi lấy danh sách rules - ' . $ruleset_response->get_error_message());
        return false;
    }

    $ruleset_body = json_decode(wp_remote_retrieve_body($ruleset_response), true);
    $this->log_message('ACCR: Phản hồi lấy danh sách rules: ' . json_encode($ruleset_body));

    if (!($ruleset_body['success'] ?? false)) {
        $this->log_message('ACCR: Không thể lấy danh sách rules');
        return false;
    }

    // Lấy danh sách rules hiện tại
    $existing_rules = $ruleset_body['result']['rules'] ?? [];

    // Tìm và xóa rule được tạo bởi plugin
    $current_domain = parse_url(home_url(), PHP_URL_HOST);
    $rule_description = "WordPress Cache Rule - {$current_domain}";
    $updated_rules = array_filter($existing_rules, function($rule) use ($rule_description) {
        return $rule['description'] !== $rule_description;
    });

    // Nếu không có rule nào bị xóa, trả về false
    if (count($updated_rules) === count($existing_rules)) {
        $this->log_message('ACCR: Không tìm thấy rule để xóa');
        return false;
    }

    // Cập nhật ruleset với danh sách rules mới
    $data = [
        'rules' => array_values($updated_rules) // Đảm bảo mảng được đánh chỉ mục lại
    ];

    $url = "https://api.cloudflare.com/client/v4/zones/{$zone_id}/rulesets/{$ruleset_id}";

    $this->log_message('ACCR: Đang cập nhật ruleset');
    $this->log_message('ACCR: URL cập nhật: ' . $url);

    $response = wp_remote_request($url, [
        'method' => 'PUT',
        'headers' => [
            'X-Auth-Email' => $email,
            'X-Auth-Key' => $api_token,
            'Content-Type' => 'application/json'
        ],
        'body' => json_encode($data),
        'timeout' => 30
    ]);

    if (is_wp_error($response)) {
        $this->log_message('ACCR: Lỗi khi cập nhật ruleset - ' . $response->get_error_message());
        return false;
    }

    $body = json_decode(wp_remote_retrieve_body($response), true);
    $this->log_message('ACCR: Phản hồi cập nhật ruleset: ' . json_encode($body));

    if (!($body['success'] ?? false)) {
        $errors = json_encode($body['errors'] ?? 'Lỗi không xác định');
        $this->log_message('ACCR: Lỗi API khi cập nhật ruleset: ' . $errors);
        return false;
    }

    // Xóa URL đã lưu
    delete_option('cf_cloudflare_rule_url');

    $this->log_message('ACCR: Xóa cache rule thành công');
    return true;
}

// Lấy Account ID
private function get_account_id($api_token, $email, $zone_id) {
    $url = "https://api.cloudflare.com/client/v4/zones/{$zone_id}";
    $response = wp_remote_get($url, [
        'headers' => [
            'X-Auth-Email' => $email,
            'X-Auth-Key' => $api_token,
            'Content-Type' => 'application/json'
        ]
    ]);

    if (is_wp_error($response)) {
        $this->log_message('ACCR: Lỗi khi lấy Account ID - ' . $response->get_error_message());
        return false;
    }

    $body = json_decode(wp_remote_retrieve_body($response), true);
    if (($body['success'] ?? false) && !empty($body['result']['account']['id'])) {
        return $body['result']['account']['id'];
    }

    $this->log_message('ACCR: Không thể lấy Account ID từ API');
    return false;
}

// Tắt Page Rule có Cache Level: Cache Everything
private function disable_cache_everything_page_rule($api_token, $email, $zone_id) {
    $url = "https://api.cloudflare.com/client/v4/zones/{$zone_id}/pagerules";
    $response = wp_remote_get($url, [
        'headers' => [
            'X-Auth-Email' => $email,
            'X-Auth-Key' => $api_token,
            'Content-Type' => 'application/json'
        ]
    ]);

    if (is_wp_error($response)) {
        $this->log_message('ACCR: Lỗi khi lấy danh sách Page Rules - ' . $response->get_error_message());
        return false;
    }

    $body = json_decode(wp_remote_retrieve_body($response), true);
    if (!($body['success'] ?? false)) {
        $this->log_message('ACCR: Không thể lấy danh sách Page Rules');
        return false;
    }

    $disabled_any = false;
    foreach ($body['result'] as $rule) {
        if ($rule['status'] === 'active') {
            foreach ($rule['actions'] as $action) {
                if ($action['id'] === 'cache_level' && $action['value'] === 'cache_everything') {
                    // Tắt rule này
                    $disable_url = "https://api.cloudflare.com/client/v4/zones/{$zone_id}/pagerules/{$rule['id']}";
                    $disable_data = $rule;
                    $disable_data['status'] = 'disabled';

                    $disable_response = wp_remote_request($disable_url, [
                        'method' => 'PUT',
                        'headers' => [
                            'X-Auth-Email' => $email,
                            'X-Auth-Key' => $api_token,
                            'Content-Type' => 'application/json'
                        ],
                        'body' => json_encode($disable_data),
                        'timeout' => 30
                    ]);

                    if (!is_wp_error($disable_response)) {
                        $disable_body = json_decode(wp_remote_retrieve_body($disable_response), true);
                        if ($disable_body['success'] ?? false) {
                            $this->log_message('ACCR: Đã tắt Page Rule có Cache Everything: ' . $rule['id']);
                            $disabled_any = true;
                        }
                    }
                    break;
                }
            }
        }
    }

    return $disabled_any;
}


public function handle_reset_settings() {
    if (isset($_POST['action']) && $_POST['action'] === 'reset_cloudflare_settings') {
        // Kiểm tra nonce để đảm bảo yêu cầu hợp lệ
        if (!isset($_POST['_wpnonce']) || !wp_verify_nonce($_POST['_wpnonce'], 'cloudflare_reset_settings')) {
            wp_die('Yêu cầu không hợp lệ.');
        }

        // Thực hiện reset cài đặt về mặc định
        $this->reset_to_default_settings();

        // Chuyển hướng người dùng trở lại trang cài đặt với tham số reset
        wp_redirect(add_query_arg('settings-reset', 'true', admin_url('tools.php?page=cloudflare-cache-settings')));
        exit;
    }
}

	// Hàm reset cài đặt về mặc định
private function reset_to_default_settings() {
    $default_values = [
        'email'            => '',
        'api_key'          => '',
        'zone_id'          => '',
        'sitemap_url'      => $this->detect_sitemap_url(), // Tự động phát hiện sitemap
        'custom_urls'      => '',
        'enable_logging'   => '0',
        'auto_preload'     => '0',
        'auto_preload_time'=> '02:00', // Giá trị mặc định là 02:00
        'last_preload_run' => '',     // Xóa lịch sử chạy cron
    ];

    // Cập nhật cài đặt về mặc định
    $update_result = update_option('cloudflare_cache_options', $default_values);

    // Ghi log kết quả cập nhật
    if ($update_result) {
        $this->log_message("Cài đặt đã được reset về mặc định: " . print_r($default_values, true));
    } else {
        $this->log_message("Không thể cập nhật cài đặt về mặc định.");
    }

    // Xóa cache để tránh dữ liệu cũ
    delete_transient('cloudflare_cache_options');

    // Thêm thông báo reset thành công
    add_settings_error(
        'cloudflare_cache_settings',
        'cloudflare_cache_settings_success',
        'Đã đặt lại cài đặt về mặc định!',
        'updated'
    );
}


}

// Khởi tạo plugin
new Cloudflare_Auto_Cache_Purge_And_Preload();
