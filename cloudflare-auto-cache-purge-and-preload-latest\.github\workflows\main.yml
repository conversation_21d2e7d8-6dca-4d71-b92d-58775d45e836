name: Update latest branch

on:
  release:
    types: [published]
  workflow_dispatch:

permissions:
  contents: write

jobs:
  update-latest:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Configure Git
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "GitHub Actions"

      - name: Fetch latest release tag
        id: get-latest-tag
        run: echo "LATEST_TAG=$(git describe --tags `git rev-list --tags --max-count=1`)" >> $GITHUB_ENV

      - name: Update latest branch
        run: |
          git checkout latest || git checkout -b latest
          git merge $LATEST_TAG
          git push origin latest
