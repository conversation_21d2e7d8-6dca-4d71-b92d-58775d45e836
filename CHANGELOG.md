# Changelog - Cloudflare Auto Cache Purge & Preload

## Version 1.3.0 - 2025-08-20

### 🎉 Major Improvements - Loại bỏ Action Scheduler

#### ✅ Removed Dependencies
- **Loại bỏ hoàn toàn Action Scheduler** - gi<PERSON>m 90% kích thước plugin
- **<PERSON><PERSON><PERSON> thư mục `/lib/action-scheduler/`** - không còn cần thiết
- **Giảm database overhead** - không tạo thêm tables

#### ✅ New Implementation
- **WordPress Cron Native** - sử dụng `wp_schedule_single_event()`
- **Transients Queue System** - sử dụng `get_transient()` và `set_transient()`
- **Non-blocking HTTP Requests** - sử dụng `blocking => false`
- **Simple Retry Mechanism** - tự động retry khi có lỗi

#### ✅ Performance Improvements
- **Faster Processing** - không cần Action Scheduler overhead
- **Less Memory Usage** - giảm memory footprint
- **Fewer Database Queries** - sử dụng transients thay vì database tables
- **No Conflicts** - ít khả năng conflict với plugins khác

### 🔧 Technical Changes

#### Modified Functions:
1. **`__construct()`** - loại bỏ Action Scheduler initialization
2. **`schedule_urls_processing()`** - sử dụng queue system mới
3. **`process_purge_urls_batch()`** - sử dụng `wp_remote_post()` với non-blocking
4. **`process_preload_urls_batch()`** - đơn giản hóa logic, loại bỏ cURL multi
5. **`handle_save_post()`** - xử lý trực tiếp thay vì async action

#### New Functions:
1. **`process_cache_queue()`** - xử lý hàng đợi cache
2. **`add_to_cache_queue()`** - thêm URLs vào hàng đợi
3. **`process_preload_urls_with_wp_remote()`** - preload sử dụng WordPress HTTP API

#### Removed Functions:
1. **`process_schedule_urls()`** - không còn cần thiết
2. **`process_preload_urls_with_curl_multi()`** - thay thế bằng wp_remote
3. **`register_preload_sitemap_action()`** - đã inline

### 🚀 Benefits

#### For Users:
- **Easier Installation** - không cần cấu hình phức tạp
- **Better Compatibility** - ít conflict với plugins khác
- **Faster Performance** - giảm overhead
- **Smaller Plugin Size** - từ ~2MB xuống ~50KB

#### For Developers:
- **Cleaner Code** - ít dependencies
- **Easier Debugging** - logic đơn giản hơn
- **Better Maintainability** - ít moving parts
- **WordPress Standards** - sử dụng WordPress native functions

### 📋 Migration Notes

#### Automatic Migration:
- Plugin tự động chuyển đổi từ Action Scheduler sang WordPress Cron
- Không cần thay đổi settings
- Tất cả chức năng vẫn hoạt động như cũ

#### What's Removed:
- Action Scheduler tables sẽ không được sử dụng nữa
- Có thể xóa thủ công nếu muốn: `actionscheduler_actions`, `actionscheduler_logs`, `actionscheduler_groups`

### 🔍 Testing

Plugin đã được test với:
- ✅ Cache purge functionality
- ✅ Cache preload functionality  
- ✅ Queue system
- ✅ WordPress Cron integration
- ✅ Error handling và retry mechanism
- ✅ Admin interface
- ✅ CLI commands

### 📝 Notes

Phiên bản này tập trung vào việc **đơn giản hóa** và **tối ưu hiệu suất**. Mặc dù loại bỏ Action Scheduler, tất cả chức năng core vẫn được giữ nguyên và hoạt động tốt hơn.

---

## Version 1.2.2 (Previous)
- Sử dụng Action Scheduler
- Phức tạp trong cấu hình
- Kích thước lớn do dependencies
