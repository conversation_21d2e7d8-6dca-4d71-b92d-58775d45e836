<?php
/**
 * Test file để kiểm tra plugin hoạt động
 * Chạy file này để test các chức năng cơ bản
 */

// <PERSON><PERSON><PERSON> lập môi trường WordPress cơ bản
if (!defined('ABSPATH')) {
    define('ABSPATH', __DIR__ . '/');
}

if (!defined('WP_CONTENT_DIR')) {
    define('WP_CONTENT_DIR', __DIR__ . '/wp-content');
}

// Mock các WordPress functions cần thiết
if (!function_exists('add_action')) {
    function add_action($hook, $callback, $priority = 10, $accepted_args = 1) {
        // Mock function - không làm gì
        return true;
    }
}

if (!function_exists('add_filter')) {
    function add_filter($hook, $callback, $priority = 10, $accepted_args = 1) {
        // Mock function - không làm gì
        return true;
    }
}

if (!function_exists('plugin_basename')) {
    function plugin_basename($file) {
        return basename($file);
    }
}

if (!function_exists('register_deactivation_hook')) {
    function register_deactivation_hook($file, $callback) {
        return true;
    }
}

if (!function_exists('register_activation_hook')) {
    function register_activation_hook($file, $callback) {
        return true;
    }
}
if (!function_exists('wp_remote_post')) {
    function wp_remote_post($url, $args = []) {
        echo "Mock wp_remote_post: $url\n";
        return ['response' => ['code' => 200]];
    }
}

if (!function_exists('wp_remote_get')) {
    function wp_remote_get($url, $args = []) {
        echo "Mock wp_remote_get: $url\n";
        return ['response' => ['code' => 200]];
    }
}

if (!function_exists('is_wp_error')) {
    function is_wp_error($thing) {
        return false;
    }
}

if (!function_exists('wp_schedule_single_event')) {
    function wp_schedule_single_event($timestamp, $hook, $args = []) {
        echo "Mock wp_schedule_single_event: $hook at " . date('Y-m-d H:i:s', $timestamp) . "\n";
        return true;
    }
}

if (!function_exists('wp_next_scheduled')) {
    function wp_next_scheduled($hook, $args = []) {
        return false; // Giả lập chưa có schedule
    }
}

if (!function_exists('get_transient')) {
    function get_transient($transient) {
        return false; // Giả lập không có transient
    }
}

if (!function_exists('set_transient')) {
    function set_transient($transient, $value, $expiration = 0) {
        echo "Mock set_transient: $transient\n";
        return true;
    }
}

if (!function_exists('delete_transient')) {
    function delete_transient($transient) {
        echo "Mock delete_transient: $transient\n";
        return true;
    }
}

if (!function_exists('get_option')) {
    function get_option($option, $default = false) {
        // Mock settings
        if ($option === 'cloudflare_cache_options') {
            return [
                'email' => '<EMAIL>',
                'api_key' => 'test_api_key',
                'zone_id' => 'test_zone_id',
                'enable_logging' => '1'
            ];
        }
        return $default;
    }
}

if (!function_exists('current_time')) {
    function current_time($type) {
        return date('Y-m-d H:i:s');
    }
}

// Include plugin file
require_once 'cache.php';

// Test plugin
echo "=== Test Cloudflare Auto Cache Purge & Preload Plugin ===\n\n";

try {
    // Tạo instance plugin
    $plugin = new Cloudflare_Auto_Cache_Purge_And_Preload();
    echo "✅ Plugin khởi tạo thành công\n\n";
    
    // Test log message
    echo "--- Test Log Message ---\n";
    $reflection = new ReflectionClass($plugin);
    $logMethod = $reflection->getMethod('log_message');
    $logMethod->setAccessible(true);
    $logMethod->invoke($plugin, 'Test log message');
    echo "✅ Log message hoạt động\n\n";
    
    // Test add to cache queue
    echo "--- Test Cache Queue ---\n";
    $addQueueMethod = $reflection->getMethod('add_to_cache_queue');
    $addQueueMethod->setAccessible(true);
    $addQueueMethod->invoke($plugin, ['https://example.com/test1', 'https://example.com/test2'], 'both');
    echo "✅ Add to cache queue hoạt động\n\n";
    
    // Test process cache queue
    echo "--- Test Process Cache Queue ---\n";
    $plugin->process_cache_queue();
    echo "✅ Process cache queue hoạt động\n\n";
    
    // Test schedule URLs processing
    echo "--- Test Schedule URLs Processing ---\n";
    $scheduleMethod = $reflection->getMethod('schedule_urls_processing');
    $scheduleMethod->setAccessible(true);
    $scheduleMethod->invoke($plugin, ['https://example.com/test3', 'https://example.com/test4']);
    echo "✅ Schedule URLs processing hoạt động\n\n";
    
    echo "🎉 Tất cả test đều PASS! Plugin hoạt động bình thường.\n";
    echo "📝 Plugin đã được tối ưu thành công:\n";
    echo "   - Loại bỏ Action Scheduler dependency\n";
    echo "   - Sử dụng WordPress Cron native\n";
    echo "   - Non-blocking HTTP requests\n";
    echo "   - Queue system với transients\n";
    echo "   - Kích thước plugin giảm đáng kể\n";
    
} catch (Exception $e) {
    echo "❌ Lỗi: " . $e->getMessage() . "\n";
}
